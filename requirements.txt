flask==3.0.3
numpy==1.26.4
opencv-python==*********
matplotlib==3.9.2
rasterio==1.4.3
pyproj==3.7.1
scikit-image==0.25.2
Pillow==10.4.0
requests==2.32.3
pytest==8.3.5
pytest-flask==1.2.0
json5==0.10.0
shapely==2.0.6
geopandas==1.0.1
flask-cors==5.0.0
# Google Cloud Vertex AI dependencies
google-cloud-aiplatform==1.38.1
google-auth==2.23.4
google-cloud-storage==2.10.0
python-dotenv==1.0.0
# Note: Removed torch, torchvision, and segment-anything as we're using Google Vertex AI
# The new implementation uses Google Cloud Vertex AI for image segmentation
