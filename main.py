# main.py
from flask import Flask, request, jsonify, send_from_directory
from flask import send_file
import os
import cv2
import numpy as np
import logging
from utils import download_satellite_image
from google.cloud import aiplatform, storage
from datetime import datetime
import base64
import json
import tempfile
from dotenv import load_dotenv

# Load environment variables from .env
load_dotenv()

# Initialize Flask
app = Flask(__name__)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("field-boundary")

# Vertex AI config from environment
PROJECT_ID = os.getenv("PROJECT_ID")
ENDPOINT_ID = os.getenv("ENDPOINT_ID")
LOCATION = os.getenv("LOCATION", "europe-west9")
BUCKET_NAME = os.getenv("BUCKET_NAME", "77744321532-sam-images")
PREDICT_MAX_DIM = int(os.getenv("PREDICT_MAX_DIM", "256"))
PREDICT_IMAGE_FORMAT = os.getenv("PREDICT_IMAGE_FORMAT", "TIFF").upper()  # TIFF or PNG or JPEG
GRID_SIZE = int(os.getenv("GRID_SIZE", "1"))  # Default to 1 tile to focus around point
MAX_MASKS_TO_PROCESS = int(os.getenv("MAX_MASKS_TO_PROCESS", "8"))
MORPH_KERNEL = int(os.getenv("MORPH_KERNEL", "3"))  # morphological kernel size
MORPH_ITERS = int(os.getenv("MORPH_ITERS", "1"))    # morphological iterations
MIN_WINDOW_FRAC = float(os.getenv("MIN_WINDOW_FRAC", "0.06"))  # local window fraction of bbox span
EARLY_ACCEPT_OVERLAP = int(os.getenv("EARLY_ACCEPT_OVERLAP", "4"))

# Validate environment variables
if not PROJECT_ID or not ENDPOINT_ID:
    raise ValueError("PROJECT_ID and ENDPOINT_ID must be set as environment variables")

# Initialize Vertex AI client
aiplatform.init(project=PROJECT_ID, location=LOCATION)
endpoint = aiplatform.Endpoint(endpoint_name=ENDPOINT_ID)

# Initialize GCS client
storage_client = storage.Client()
bucket = storage_client.bucket(BUCKET_NAME)
TEMP_IMAGES_DIR = os.path.join(os.path.dirname(__file__), 'temp_images')
os.makedirs(TEMP_IMAGES_DIR, exist_ok=True)

# Ensure temp_images exists for local mask downloads
TEMP_IMAGES_DIR = os.path.join(os.getcwd(), "temp_images")
os.makedirs(TEMP_IMAGES_DIR, exist_ok=True)

def save_image_to_bucket(image_array, filename, image_format_ext=".png"):
    """
    Save image array to GCS bucket and return the GCS URI
    """
    try:
        # Convert RGB to BGR for OpenCV
        image_bgr = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=image_format_ext, delete=False) as temp_file:
            temp_path = temp_file.name
            # Attempt write; if TIFF not supported, fall back to PNG
            success = cv2.imwrite(temp_path, image_bgr)
            if not success and image_format_ext.lower() != ".png":
                alt_path = temp_path.rsplit(image_format_ext, 1)[0] + ".png"
                if cv2.imwrite(alt_path, image_bgr):
                    temp_path = alt_path
                    image_format_ext = ".png"
                else:
                    raise RuntimeError("Failed to write image to temporary file")
        
        # Upload to GCS
        blob_name = f"field_images/{filename}"
        blob = bucket.blob(blob_name)
        blob.upload_from_filename(temp_path)
        
        # Clean up temporary file
        os.unlink(temp_path)
        
        gcs_uri = f"gs://{BUCKET_NAME}/{blob_name}"
        logger.info(f"📤 Image saved to GCS: {gcs_uri}")
        return gcs_uri, blob_name
        
    except Exception as e:
        logger.error(f"Failed to save image to bucket: {str(e)}")
        raise

def get_image_from_bucket(blob_name):
    """
    Download image from GCS bucket and return as numpy array
    """
    try:
        blob = bucket.blob(blob_name)
        with tempfile.NamedTemporaryFile() as temp_file:
            blob.download_to_filename(temp_file.name)
            image = cv2.imread(temp_file.name)
            return cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    except Exception as e:
        logger.error(f"Failed to get image from bucket: {str(e)}")
        raise

def process_mask_to_contours(mask_obj, bbox, image_width, image_height, simplify_epsilon_ratio=0.004):
    """
    Process a mask object (base64 string, 2D list, or ndarray) to extract contour
    points in geographic coordinates.
    """
    try:
        mask_array = None

        # Case 1: base64-encoded image string
        if isinstance(mask_obj, str):
            try:
                mask_bytes = base64.b64decode(mask_obj)
                decoded = cv2.imdecode(np.frombuffer(mask_bytes, np.uint8), cv2.IMREAD_GRAYSCALE)
                mask_array = decoded
            except Exception as dec_err:
                logger.warning(f"Failed to decode base64 mask: {dec_err}")

        # Case 2: list (likely 2D list of 0/1 or 0-255)
        if mask_array is None and isinstance(mask_obj, list):
            try:
                mask_array = np.array(mask_obj, dtype=np.uint8)
                if mask_array.ndim == 3:
                    # Some models may return HxWx1
                    mask_array = mask_array.squeeze(-1)
            except Exception as arr_err:
                logger.warning(f"Failed to convert list mask to array: {arr_err}")

        # Case 3: already an ndarray
        if mask_array is None and isinstance(mask_obj, np.ndarray):
            mask_array = mask_obj

        if mask_array is None:
            logger.error("Failed to obtain mask array from mask object")
            return []

        # If mask resolution doesn't match the image, resize mask to image dimensions
        try:
            if mask_array is not None and (mask_array.shape[1] != image_width or mask_array.shape[0] != image_height):
                logger.debug(f"Resizing mask from {mask_array.shape[::-1]} to {(image_width, image_height)}")
                mask_array = cv2.resize(mask_array, (int(image_width), int(image_height)), interpolation=cv2.INTER_NEAREST)
        except Exception as resize_err:
            logger.warning(f"Failed to resize mask to image dims: {resize_err}")

        # Normalize to binary mask using Otsu (more robust than fixed threshold)
        if mask_array.dtype != np.uint8:
            mask_array = mask_array.astype(np.uint8)
        if mask_array.max() <= 1:
            mask_uint8 = (mask_array > 0).astype(np.uint8) * 255
        else:
            _, mask_uint8 = cv2.threshold(mask_array, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # Morphological clean-up to reduce spurious edges
        try:
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (MORPH_KERNEL, MORPH_KERNEL))
            mask_uint8 = cv2.morphologyEx(mask_uint8, cv2.MORPH_OPEN, kernel, iterations=MORPH_ITERS)
            mask_uint8 = cv2.morphologyEx(mask_uint8, cv2.MORPH_CLOSE, kernel, iterations=MORPH_ITERS)
        except Exception:
            pass

        # Find contours
        contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_TC89_L1)
        if not contours:
            logger.warning("No contours found in mask")
            return []

        # Largest contour as boundary
        largest_contour = max(contours, key=cv2.contourArea)
        epsilon = simplify_epsilon_ratio * cv2.arcLength(largest_contour, True)
        simplified_contour = cv2.approxPolyDP(largest_contour, epsilon, True)

        # Convert pixel coordinates to geographic coordinates
        contour_points_geo = []
        for point in simplified_contour:
            px, py = point[0]
            lon_geo = bbox["xmin"] + (px / image_width) * (bbox["xmax"] - bbox["xmin"])
            lat_geo = bbox["ymax"] - (py / image_height) * (bbox["ymax"] - bbox["ymin"])
            contour_points_geo.append([float(lon_geo), float(lat_geo)])

        # Close polygon
        if contour_points_geo and contour_points_geo[0] != contour_points_geo[-1]:
            contour_points_geo.append(contour_points_geo[0])

        logger.info(f"🎯 Extracted {len(contour_points_geo)} contour points")
        return contour_points_geo

    except Exception as e:
        logger.error(f"Failed to process mask: {str(e)}")
        return []


def mask_obj_to_uint8(mask_obj):
    """Convert various mask object formats (base64 string, list, ndarray) to a uint8 mask image (0/255).
    Returns None on failure."""
    try:
        # base64 string
        if isinstance(mask_obj, str):
            try:
                mask_bytes = base64.b64decode(mask_obj)
                decoded = cv2.imdecode(np.frombuffer(mask_bytes, np.uint8), cv2.IMREAD_GRAYSCALE)
                if decoded is None:
                    return None
                mask = decoded
            except Exception:
                return None

        elif isinstance(mask_obj, list):
            try:
                arr = np.array(mask_obj, dtype=np.uint8)
                if arr.ndim == 3 and arr.shape[2] == 1:
                    arr = arr.squeeze(-1)
                mask = arr
            except Exception:
                return None

        elif isinstance(mask_obj, np.ndarray):
            mask = mask_obj
        else:
            return None

        if mask.dtype != np.uint8:
            mask = mask.astype(np.uint8)
        if mask.max() <= 1:
            mask_uint8 = (mask > 0).astype(np.uint8) * 255
        else:
            _, mask_uint8 = cv2.threshold(mask, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        return mask_uint8
    except Exception as e:
        logger.warning(f"mask_obj_to_uint8 failed: {e}")
        return None

def point_in_polygon(point_lon, point_lat, polygon_coords):
    """
    Ray casting algorithm to test if a lon/lat point is inside polygon_coords (closed ring).
    polygon_coords: list of [lon, lat]
    """
    inside = False
    num_points = len(polygon_coords)
    if num_points < 3:
        return False
    for i in range(num_points - 1):
        x1, y1 = polygon_coords[i]
        x2, y2 = polygon_coords[i + 1]
        # Check if edge crosses the horizontal ray to the right of the point
        intersects = ((y1 > point_lat) != (y2 > point_lat)) and (
            point_lon < (x2 - x1) * (point_lat - y1) / (y2 - y1 + 1e-12) + x1
        )
        if intersects:
            inside = not inside
    return inside

@app.route("/health", methods=["GET"])
def health_check():
    return jsonify({"status": "healthy", "bucket": BUCKET_NAME, "project": PROJECT_ID})

@app.route("/initialize", methods=["POST"])
def initialize():
    """
    Minimal initialize endpoint for compatibility with tests.
    """
    try:
        return jsonify({
            "status": "Model initialized successfully",
            "device": "cpu",
            "model_type": "remote-vertex-endpoint",
            "endpoint_id": ENDPOINT_ID
        })
    except Exception as e:
        logger.error(f"Failed to initialize: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route("/segment-coordinates", methods=["POST"])
def segment_coordinates():
    """
    Segment a field boundary from latitude/longitude coordinates
    """
    try:
        data = request.json
        latitude = float(data.get("latitude"))
        longitude = float(data.get("longitude"))
        zoom_level = int(data.get("zoom_level", 16))
        tile_source = data.get("tile_source", "Satellite")

        logger.info(f"🌍 Processing request: lat={latitude}, lon={longitude}, zoom={zoom_level}")

        # Step 1: Download satellite image
        # The request may include 'grid_size' to fetch a larger tile grid (e.g., 3 for 3x3 tiles)
        requested_grid = int(data.get("grid_size", GRID_SIZE))
        # Cap grid size to avoid very large downloads
        if requested_grid < 1:
            requested_grid = 1
        max_grid = 7
        if requested_grid > max_grid:
            logger.warning(f"Requested grid_size {requested_grid} > {max_grid}, capping to {max_grid}")
            requested_grid = max_grid
        logger.info(f"📡 Downloading satellite image with grid_size={requested_grid}...")
        download_result = download_satellite_image(latitude, longitude, zoom_level, grid_size=requested_grid)
        image_rgb = download_result["image_array"]
        bbox = download_result["bbox"]
        width, height = download_result["width"], download_result["height"]
        # center_pixel returned by downloader: position of input lat/lon in returned image (float coords)
        center_pixel = download_result.get('center_pixel', {'x': None, 'y': None})
        # preserve original dimensions in case we resize for prediction
        orig_width, orig_height = width, height

        # Optional: Downscale to reduce Vertex response size
        max_dim = max(width, height)
        target_max = PREDICT_MAX_DIM
        # Preserve original center pixel returned by downloader for diagnostics
        center_pixel_orig = None
        center_pixel_scaled = None
        try:
            if center_pixel and center_pixel.get('x') is not None:
                center_pixel_orig = {'x': float(center_pixel.get('x')), 'y': float(center_pixel.get('y'))}
        except Exception:
            center_pixel_orig = None

        if max_dim > target_max:
            scale = target_max / float(max_dim)
            new_w = int(round(width * scale))
            new_h = int(round(height * scale))
            logger.info(f"🧩 Resizing image from {width}x{height} to {new_w}x{new_h} for Vertex predict")
            image_rgb = cv2.resize(image_rgb, (new_w, new_h), interpolation=cv2.INTER_AREA)
            # If downloader provided a center pixel, scale it to the resized image coordinates
            if center_pixel_orig is not None:
                center_pixel_scaled = {
                    'x': float(center_pixel_orig['x']) * scale,
                    'y': float(center_pixel_orig['y']) * scale
                }
            width, height = new_w, new_h
        else:
            # No resize; scaled == original
            if center_pixel_orig is not None:
                center_pixel_scaled = {'x': center_pixel_orig['x'], 'y': center_pixel_orig['y']}

        # Step 2: Save image to GCS bucket
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        # Choose extension based on desired predict image format
        if PREDICT_IMAGE_FORMAT == "TIFF":
            image_ext = ".tiff"
        elif PREDICT_IMAGE_FORMAT == "JPEG":
            image_ext = ".jpg"
        else:
            image_ext = ".png"

        filename = f"field_{timestamp}{image_ext}"
        gcs_uri, blob_name = save_image_to_bucket(image_rgb, filename, image_format_ext=image_ext)

        # Step 3: Prepare image reference for Vertex AI and predict (with fallbacks)
        logger.info("🤖 Sending image reference to Vertex AI for segmentation...")

        # Prepare inline base64 in requested format to reduce payload size / match model expectations
        img_b64 = None
        try:
            image_bgr = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2BGR)
            if PREDICT_IMAGE_FORMAT == "TIFF":
                ok, img_buf = cv2.imencode('.tiff', image_bgr)
            elif PREDICT_IMAGE_FORMAT == "JPEG":
                ok, img_buf = cv2.imencode('.jpg', image_bgr, [int(cv2.IMWRITE_JPEG_QUALITY), 85])
            else:
                ok, img_buf = cv2.imencode('.png', image_bgr)
            if ok:
                img_b64 = base64.b64encode(img_buf.tobytes()).decode('utf-8')
        except Exception as enc_err:
            logger.warning(f"Failed to encode image to base64: {enc_err}")

        prediction = None
        last_error = None
        # Vertex SAM: convert lat/lon to pixel coordinates on the image we will send
        # and include as a point prompt. Some SAM wrappers expect normalized coords [0..1].
        POINT_COORDS_NORMALIZED = os.getenv("POINT_COORDS_NORMALIZED", "false").lower() in ("1","true","yes")

        # compute pixel coordinates (x: horizontal, y: vertical) — prefer downloader's center_pixel for accuracy
        try:
            # Use the scaled center pixel (if we resized above) so coords align with the image we will send
            if center_pixel_scaled is not None:
                px = int(round(float(center_pixel_scaled.get('x'))))
                py = int(round(float(center_pixel_scaled.get('y'))))
            else:
                # Use inclusive pixel denominator (width-1 / height-1) to map lon/lat to pixel coords
                px = int(round((longitude - bbox['xmin']) / (bbox['xmax'] - bbox['xmin']) * (width - 1)))
                py = int(round((bbox['ymax'] - latitude) / (bbox['ymax'] - bbox['ymin']) * (height - 1)))
        except Exception:
            px, py = None, None

        if px is not None and py is not None:
            if POINT_COORDS_NORMALIZED:
                pt_x = float(px) / float(max(width - 1, 1))
                pt_y = float(py) / float(max(height - 1, 1))
            else:
                pt_x = int(px)
                pt_y = int(py)
            point_coords = [[pt_x, pt_y]]
            point_labels = [1]  # 1 -> foreground
        else:
            point_coords = None
            point_labels = None

        # Diagnostics: include original computed pixel and center_pixel for debugging later
        computed_point_pixels = {
            'px': px,
            'py': py,
            'center_pixel_orig': center_pixel_orig,
            'center_pixel_scaled': center_pixel_scaled
        }

        candidate_instances = []
        # Build instance dicts that include the point prompt so Vertex-deployed SAM knows where to segment
        if img_b64:
            inst = {"image": img_b64}
            if point_coords is not None:
                inst["point_coords"] = point_coords
                inst["point_labels"] = point_labels
            candidate_instances.append([inst])
            # alternative key
            inst2 = {"content": img_b64}
            if point_coords is not None:
                inst2["point_coords"] = point_coords
                inst2["point_labels"] = point_labels
            candidate_instances.append([inst2])

        # GCS URI variant with point prompt
        inst_gcs = {"image": gcs_uri}
        if point_coords is not None:
            inst_gcs["point_coords"] = point_coords
            inst_gcs["point_labels"] = point_labels
        candidate_instances.append([inst_gcs])

        parameter_attempts = [
            {"max_masks": 3, "multimask_output": True, "mask_format": "rle", "return_rle": True},
            {"max_masks": 3, "multimask_output": True},
            {"max_masks": 1, "multimask_output": False},
            None,
        ]

        # Try each candidate instance schema with several parameter combinations
        for attempt_idx, instances in enumerate(candidate_instances, start=1):
            schema_keys = list(instances[0].keys()) if instances and isinstance(instances, list) else []
            for p_idx, params in enumerate(parameter_attempts, start=1):
                try:
                    logger.info(f"🔁 Vertex predict attempt {attempt_idx}.{p_idx} schema={schema_keys} params={params}")
                    if params is not None:
                        prediction = endpoint.predict(instances=instances, parameters=params)
                    else:
                        prediction = endpoint.predict(instances=instances)
                    logger.info(f"Vertex predict attempt {attempt_idx}.{p_idx} succeeded")
                    # successful prediction, break out of both loops
                    break
                except Exception as e:
                    last_error = e
                    err_msg = str(e)
                    try:
                        # Some Vertex errors contain JSON payloads; try to parse for nicer logs
                        if '{' in err_msg and '}' in err_msg:
                            start = err_msg.find('{')
                            j = err_msg[start:]
                            parsed = json.loads(j)
                            logger.warning(f"Vertex predict attempt {attempt_idx}.{p_idx} failed (details): {json.dumps(parsed)}")
                        else:
                            logger.warning(f"Vertex predict attempt {attempt_idx}.{p_idx} failed: {err_msg}")
                    except Exception:
                        logger.warning(f"Vertex predict attempt {attempt_idx}.{p_idx} failed: {err_msg}")
            else:
                # inner loop did not break (no success for this instance schema) -> try next instance schema
                continue
            # inner loop broke (success) -> stop trying more instance schemas
            break

        # Parse masks and optional scores from Vertex response
        masks_list = []
        scores_list = []
        if prediction and getattr(prediction, 'predictions', None):
            try:
                pred0 = prediction.predictions[0]
                if isinstance(pred0, dict) and "masks" in pred0:
                    masks_list = pred0.get("masks", [])
                    scores = pred0.get("scores") or pred0.get("mask_scores")
                    if isinstance(scores, list):
                        scores_list = scores
                elif isinstance(pred0, list):
                    # List of objects {mask, score}
                    for obj in pred0:
                        if isinstance(obj, dict):
                            m = obj.get("mask") or obj.get("masks")
                            if m is not None:
                                masks_list.append(m)
                                s = obj.get("score") or obj.get("confidence")
                                scores_list.append(s if isinstance(s, (int, float)) else None)
                else:
                    masks_list = []
            except Exception:
                masks_list = []
        else:
            if last_error:
                logger.error(f"❌ Segmentation via Vertex AI failed. Reason: {str(last_error)}")

        if masks_list:
            logger.info(f"🎭 Received {len(masks_list)} masks from Vertex AI")

        # Diagnostics: record the shape/type of masks returned so we can debug alignment issues
        masks_shapes = []
        try:
            for m in masks_list:
                try:
                    if isinstance(m, str):
                        # base64-encoded image? try to decode
                        m_bytes = base64.b64decode(m)
                        decoded = cv2.imdecode(np.frombuffer(m_bytes, np.uint8), cv2.IMREAD_UNCHANGED)
                        if decoded is not None:
                            masks_shapes.append({'type': 'decoded_image', 'shape': decoded.shape})
                        else:
                            masks_shapes.append({'type': 'base64_string', 'shape': None})
                    elif isinstance(m, list):
                        arr = np.array(m)
                        masks_shapes.append({'type': 'list', 'shape': arr.shape})
                    elif isinstance(m, np.ndarray):
                        masks_shapes.append({'type': 'ndarray', 'shape': m.shape})
                    else:
                        masks_shapes.append({'type': type(m).__name__, 'shape': None})
                except Exception:
                    masks_shapes.append({'type': type(m).__name__, 'shape': None})
        except Exception:
            masks_shapes = []

        # Step 5: Process masks, rank by (contains point, IoU with a local window, area)
        all_contours = []
        accepted = None
        for i, mask_entry in enumerate(masks_list[:MAX_MASKS_TO_PROCESS]):
            contour_points = process_mask_to_contours(mask_entry, bbox, width, height)
            if contour_points:
                # Compute polygon area in lon/lat space (approximate using shoelace)
                area = 0.0
                pts = contour_points
                if len(pts) > 2:
                    area = 0.0
                    for j in range(len(pts) - 1):
                        x1, y1 = pts[j]
                        x2, y2 = pts[j + 1]
                        area += x1 * y2 - x2 * y1
                    area = abs(area) / 2.0
                contains_pt = point_in_polygon(longitude, latitude, contour_points)
                # Compute a small proximity window around the source point to prefer local field
                lon_span = (bbox["xmax"] - bbox["xmin"]) * MIN_WINDOW_FRAC
                lat_span = (bbox["ymax"] - bbox["ymin"]) * MIN_WINDOW_FRAC
                window = [
                    [longitude - lon_span/2, latitude - lat_span/2],
                    [longitude + lon_span/2, latitude - lat_span/2],
                    [longitude + lon_span/2, latitude + lat_span/2],
                    [longitude - lon_span/2, latitude + lat_span/2],
                    [longitude - lon_span/2, latitude - lat_span/2],
                ]
                # Simple overlap score: count vertices inside window
                overlap_score = sum(
                    1 for (x, y) in contour_points
                    if (window[0][0] <= x <= window[1][0]) and (window[0][1] <= y <= window[2][1])
                )
                score_val = None
                if i < len(scores_list):
                    s = scores_list[i]
                    if isinstance(s, (int, float)):
                        score_val = float(s)
                all_contours.append({
                    "mask_index": i,
                    "contour_points": contour_points,
                    "point_count": len(contour_points),
                    "area": area,
                    "score": score_val,
                    "contains_source_point": contains_pt,
                    "overlap_score": overlap_score
                })
                # Early accept if polygon contains point and has enough local overlap
                if contains_pt and overlap_score >= EARLY_ACCEPT_OVERLAP and accepted is None:
                    accepted = all_contours[-1]
                    break

        if not all_contours:
            # Enforce Vertex-only segmentation: surface the error to client
            schema_tried = []
            for inst in candidate_instances:
                if isinstance(inst, list) and inst:
                    schema_tried.append(list(inst[0].keys()))
            detail = str(last_error) if last_error else "No masks returned"
            logger.error(f"❌ Segmentation via Vertex AI failed. Tried schemas={schema_tried}. Reason: {detail}")
            return jsonify({
                "error": "Vertex AI segmentation failed",
                "detail": detail,
                "schemas_tried": schema_tried
            }), 502

        # Step 6: Select best contour prioritizing the polygon that contains the source point
        if accepted is not None:
            best = accepted
        else:
            def best_key(c):
                contains = 1 if c.get("contains_source_point") else 0
                return (contains, c.get("overlap_score") or 0, c.get("score") or 0.0, c.get("area") or 0.0)
            best = max(all_contours, key=best_key, default=None)
        if not best:
            return jsonify({"error": "No valid contours extracted from masks"}), 400

        feature = {
            "type": "Feature",
            "properties": {
                "field_boundary": True,
                "mask_index": best["mask_index"],
                "point_count": best["point_count"],
                "segmentation_score": float(best.get("score") or 0.0),
                "area": best.get("area", 0.0),
                "source_coordinates": {
                    "latitude": latitude, 
                    "longitude": longitude
                },
                "zoom_level": zoom_level,
                "tile_source": tile_source,
                "image_metadata": {
                    "gcs_uri": gcs_uri,
                    "blob_name": blob_name,
                    "bbox": bbox,
                    "dimensions": {"width": width, "height": height}
                },
                "processing_timestamp": datetime.now().isoformat()
            },
            "geometry": {
                "type": "Polygon",
                "coordinates": [best["contour_points"]]
            }
        }

        features = [feature]

        # Save the selected mask as PNG and an annotated input image with a red point at the source lat/lon
        download_mask_filename = None
        download_image_filename = None
        try:
            # Best mask index points to 'best' contour; recreate mask image from contour on a blank canvas
            mask_canvas = np.zeros((height, width), dtype=np.uint8)
            # draw filled polygon from pixel coordinates of best contour
            poly_pts = []
            for lon, lat in best['contour_points']:
                # convert geo back to pixel using inclusive denominators
                px = int(round((lon - bbox['xmin']) / (bbox['xmax'] - bbox['xmin']) * (width - 1)))
                py = int(round((bbox['ymax'] - lat) / (bbox['ymax'] - bbox['ymin']) * (height - 1)))
                poly_pts.append([px, py])
            if poly_pts:
                pts = np.array(poly_pts, dtype=np.int32)
                cv2.fillPoly(mask_canvas, [pts], color=255)
                download_mask_filename = f"mask_{timestamp}.png"
                mask_path = os.path.join(TEMP_IMAGES_DIR, download_mask_filename)
                cv2.imwrite(mask_path, mask_canvas)

            # Save annotated input image with red point at the source — use computed px/py so it matches prompt sent to Vertex
            annotated = image_rgb.copy()
            if px is not None and py is not None:
                draw_px = int(px)
                draw_py = int(py)
            else:
                # fallback to bbox-based computation
                draw_px = int(round((longitude - bbox['xmin']) / (bbox['xmax'] - bbox['xmin']) * (width - 1)))
                draw_py = int(round((bbox['ymax'] - latitude) / (bbox['ymax'] - bbox['ymin']) * (height - 1)))
            # Draw a red circle on resized annotated image
            cv2.circle(annotated, (draw_px, draw_py), radius=8, color=(255, 0, 0), thickness=-1)  # RGB red
            download_image_filename = f"input_annotated_{timestamp}.png"
            annotated_path = os.path.join(TEMP_IMAGES_DIR, download_image_filename)
            cv2.imwrite(annotated_path, cv2.cvtColor(annotated, cv2.COLOR_RGB2BGR))

            # Also create an annotated image at original resolution so lat/lon overlays match the original TIFF
            try:
                # If we resized, download_result still has the original image size and data was modified in image_rgb.
                # Reconstruct original-sized image by resizing back from the original array stored earlier if needed.
                # We kept orig_width/orig_height before resizing.
                orig_img = download_result.get('image_array')
                if orig_img is None:
                    orig_img = cv2.resize(image_rgb, (orig_width, orig_height), interpolation=cv2.INTER_LINEAR)
                # compute source pixel in original image coords: prefer using downloader-provided original center pixel
                if center_pixel_orig is not None:
                    src_px_orig = int(round(center_pixel_orig['x']))
                    src_py_orig = int(round(center_pixel_orig['y']))
                else:
                    src_px_orig = int(round((longitude - bbox['xmin']) / (bbox['xmax'] - bbox['xmin']) * (orig_width - 1)))
                    src_py_orig = int(round((bbox['ymax'] - latitude) / (bbox['ymax'] - bbox['ymin']) * (orig_height - 1)))
                annotated_orig = orig_img.copy()
                cv2.circle(annotated_orig, (src_px_orig, src_py_orig), radius=12, color=(255, 0, 0), thickness=-1)
                download_image_orig_filename = f"input_annotated_orig_{timestamp}.png"
                annotated_orig_path = os.path.join(TEMP_IMAGES_DIR, download_image_orig_filename)
                cv2.imwrite(annotated_orig_path, cv2.cvtColor(annotated_orig, cv2.COLOR_RGB2BGR))
                # Add original annotated filename to response later
                download_image_orig_filename_var = download_image_orig_filename
            except Exception:
                download_image_orig_filename_var = None
        except Exception as save_err:
            logger.warning(f"Failed to save mask or annotated image: {save_err}")

        # Prepare download URL variables (will be injected into response_data after it's created)
        download_mask_url = f"/download-mask/{download_mask_filename}" if download_mask_filename else None
        download_image_url = f"/download-image/{download_image_filename}" if download_image_filename else None

        # Save the mask image corresponding to the best contour for local download (if available)
        try:
            if masks_list and best and isinstance(best.get("mask_index"), int):
                mask_idx = best["mask_index"]
                if 0 <= mask_idx < len(masks_list):
                    mask_obj = masks_list[mask_idx]
                    mask_img = mask_obj_to_uint8(mask_obj)
                    if mask_img is not None:
                        mask_filename = f"mask_{timestamp}_{mask_idx}.png"
                        mask_path = os.path.join(TEMP_IMAGES_DIR, mask_filename)
                        cv2.imwrite(mask_path, mask_img)
                        # Add download URL to feature properties
                        features[0]["properties"]["mask_download"] = f"/download-mask/{mask_filename}"
        except Exception as save_err:
            logger.warning(f"Failed to save mask image locally: {save_err}")

        # Cleanup: delete the uploaded image from GCS after generating contours
        try:
            cleanup_blob = bucket.blob(blob_name)
            if cleanup_blob.exists():
                cleanup_blob.delete()
                logger.info(f"🧹 Deleted temporary image from GCS: gs://{BUCKET_NAME}/{blob_name}")
        except Exception as cleanup_err:
            logger.warning(f"Failed to delete temporary image {blob_name}: {cleanup_err}")

        # Response with all detected boundaries
        response_data = {
            "type": "FeatureCollection",
            "features": features,
            "metadata": {
                "total_boundaries_detected": len(features),
                "image_saved_to": gcs_uri,
                "grid_size_used": requested_grid,
                "processing_status": "success",
                "diagnostics": {
                    "bbox": bbox,
                    "image_dimensions": {"width": width, "height": height},
                    "computed_point_pixels": computed_point_pixels,
                    "masks_shapes": masks_shapes
                }
            }
        }

        # Inject download URLs into metadata
        response_data['metadata']['mask_download_url'] = download_mask_url
        response_data['metadata']['annotated_image_url'] = download_image_url

        logger.info(f"✅ Successfully processed field boundary detection")
        logger.info(f"📊 Found {len(features)} field boundaries")
        
        return jsonify(response_data)

    except Exception as e:
        logger.error(f"❌ Segmentation failed: {str(e)}", exc_info=True)
        return jsonify({
            "error": str(e),
            "processing_status": "failed",
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route("/get-saved-image/<path:blob_name>", methods=["GET"])
def get_saved_image(blob_name):
    """
    Retrieve a previously saved image from GCS bucket
    """
    try:
        blob = bucket.blob(blob_name)
        if not blob.exists():
            return jsonify({"error": "Image not found in bucket"}), 404
        
        # Get image metadata
        blob.reload()
        metadata = {
            "blob_name": blob_name,
            "size_bytes": blob.size,
            "created": blob.time_created.isoformat() if blob.time_created else None,
            "updated": blob.updated.isoformat() if blob.updated else None,
            "gcs_uri": f"gs://{BUCKET_NAME}/{blob_name}"
        }
        
        return jsonify({
            "status": "found",
            "metadata": metadata
        })
        
    except Exception as e:
        logger.error(f"Failed to get image metadata: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route('/download-image/<path:filename>', methods=['GET'])
def download_image(filename):
    try:
        path = os.path.join(TEMP_IMAGES_DIR, filename)
        if not os.path.exists(path):
            return jsonify({"error": "Image not found"}), 404
        return send_file(path, mimetype='image/png', as_attachment=True, download_name=filename)
    except Exception as e:
        logger.error(f"Failed to serve image {filename}: {e}")
        return jsonify({"error": str(e)}), 404


@app.route('/download-mask/<path:filename>', methods=['GET'])
def download_mask(filename):
    """Serve a previously saved mask PNG from the local temp_images directory."""
    try:
        path = os.path.join(TEMP_IMAGES_DIR, filename)
        if not os.path.exists(path):
            return jsonify({"error": "Mask not found"}), 404
        return send_file(path, mimetype='image/png', as_attachment=True, download_name=filename)
    except Exception as e:
        logger.error(f"Failed to serve mask {filename}: {e}")
        return jsonify({"error": str(e)}), 500

@app.route("/list-saved-images", methods=["GET"])
def list_saved_images():
    """
    List all saved images in the bucket
    """
    try:
        blobs = bucket.list_blobs(prefix="field_images/")
        images = []
        
        for blob in blobs:
            if blob.name.endswith(('.png', '.jpg', '.jpeg')):
                images.append({
                    "blob_name": blob.name,
                    "size_bytes": blob.size,
                    "created": blob.time_created.isoformat() if blob.time_created else None,
                    "gcs_uri": f"gs://{BUCKET_NAME}/{blob.name}"
                })
        
        return jsonify({
            "total_images": len(images),
            "images": images
        })
        
    except Exception as e:
        logger.error(f"Failed to list images: {str(e)}")
        return jsonify({"error": str(e)}), 500

if __name__ == "__main__":
    logger.info(f"🚀 Starting Field Boundary Detection API")
    logger.info(f"🪣 Using GCS bucket: {BUCKET_NAME}")
    logger.info(f"🤖 Using Vertex AI endpoint: {ENDPOINT_ID}")
    app.run(debug=True, host="0.0.0.0", port=5000)