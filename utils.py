import numpy as np
import cv2
import math
import matplotlib.pyplot as plt
from pyproj import Transformer
import json
import requests
from PIL import Image
import io
from google.cloud import storage
import tempfile
import os
import logging

logger = logging.getLogger("field-boundary")

def show_mask(mask, ax, random_color=False):
    """
    Display a mask on a matplotlib axis.
    """
    if random_color:
        color = np.concatenate([np.random.random(3), np.array([0.6])], axis=0)
    else:
        color = np.array([30/255, 144/255, 255/255, 0.6])
    h, w = mask.shape[-2:]
    mask_image = mask.reshape(h, w, 1) * color.reshape(1, 1, -1)
    ax.imshow(mask_image)

def show_points(coords, labels, ax, marker_size=375):
    """
    Display points on a matplotlib axis.
    """
    pos_points = coords[labels==1]
    neg_points = coords[labels==0]
    ax.scatter(pos_points[:, 0], pos_points[:, 1], color='green', marker='*', s=marker_size, edgecolor='white', linewidth=1.25)
    ax.scatter(neg_points[:, 0], neg_points[:, 1], color='red', marker='*', s=marker_size, edgecolor='white', linewidth=1.25)

def show_box(box, ax):
    """
    Display a bounding box on a matplotlib axis.
    """
    x0, y0 = box[0], box[1]
    w, h = box[2] - box[0], box[3] - box[1]
    ax.add_patch(plt.Rectangle((x0, y0), w, h, edgecolor='green', facecolor=(0,0,0,0), lw=2))

# SAM initialization removed: this project uses Vertex AI remote SAM. If you need
# a local SAM model initializer, re-add it here and ensure the segment_anything
# package and torch are installed in your environment.

def latlon_to_pixel(lat, lon, image_width, image_height, min_lat, max_lat, min_lon, max_lon):
    """
    Convert latitude and longitude to pixel coordinates.
    """
    x = int(((lon - min_lon) / (max_lon - min_lon)) * image_width)
    y = int(((max_lat - lat) / (max_lat - min_lat)) * image_height)
    return x, y

def pixel_to_latlon(px, py, image_width, image_height, min_lat, max_lat, min_lon, max_lon):
    """
    Convert pixel coordinates to latitude and longitude.
    """
    lon = min_lon + (px / image_width) * (max_lon - min_lon)
    lat = max_lat - (py / image_height) * (max_lat - min_lat)
    return lat, lon

def geographic_to_epsg4326(lon1, lat1):
    """Convert coordinates from WebMercator EPSG:3857 to EPSG:4326 (lon,lat)."""
    try:
        transformer = Transformer.from_crs("EPSG:3857", "EPSG:4326", always_xy=True)
        lon_4326, lat_4326 = transformer.transform(lon1, lat1)
        return lon_4326, lat_4326
    except Exception:
        # If transformation fails, return input as a safe fallback
        return lon1, lat1

def save_contour_points(contour_points, output_file='contour_points.json'):
    """
    Save contour points to a GeoJSON file.
    """
    polygon = {
        "type": "Polygon",
        "coordinates": [contour_points]
    }
    with open(output_file, 'w') as file:
        json.dump(polygon, file)

def deg2num(lat_deg, lon_deg, zoom):
    """Convert latitude/longitude to tile numbers"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    xtile = int((lon_deg + 180.0) / 360.0 * n)
    ytile = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (xtile, ytile)

def num2deg(xtile, ytile, zoom):
    """Convert tile numbers to latitude/longitude"""
    n = 2.0 ** zoom
    lon_deg = xtile / n * 360.0 - 180.0
    lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * ytile / n)))
    lat_deg = math.degrees(lat_rad)
    return (lat_deg, lon_deg)

def _latlon_to_global_pixel(lat_deg, lon_deg, zoom, tile_size=256):
    """Convert lat/lon to global pixel coordinates for slippy map tiles (Web Mercator).

    Returns (px, py) in pixels at the given zoom level where the full world is tile_size * 2**zoom pixels wide/high.
    """
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    px = (lon_deg + 180.0) / 360.0 * n * tile_size
    py = (1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n * tile_size
    return float(px), float(py)


def _global_pixel_to_latlon(px, py, zoom, tile_size=256):
    """Convert global pixel coordinates back to lat/lon at a given zoom (returns lat, lon)."""
    n = 2.0 ** zoom
    lon_deg = px / (tile_size * n) * 360.0 - 180.0
    lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * (py / (tile_size * n)))))
    lat_deg = math.degrees(lat_rad)
    return float(lat_deg), float(lon_deg)


def download_satellite_image(center_lat, center_lon, zoom_level, grid_size=3):
    """
    Download a satellite image grid centered on the exact provided lat/lon.

    This function computes the global pixel coordinate of the requested lat/lon at the given zoom
    and crops the assembled tiles so that the final returned image has the requested grid_size * 256
    dimensions and the input lat/lon is exactly at the center pixel of the returned image.

    Args:
        center_lat: Center latitude
        center_lon: Center longitude
        zoom_level: Zoom level
        grid_size: Size of tile grid (e.g., 3 -> 3x3 tiles). Final image will be grid_size*256 px wide/high.

    Returns:
        dict with keys: bbox (xmin/xmax/ymin/ymax), image_array (H x W x 3 RGB), width, height, successful_downloads, total_tiles
    """
    try:
        logger.info(f"Downloading {grid_size}x{grid_size} satellite image centered at lat={center_lat}, lon={center_lon}, zoom={zoom_level}")

        tile_size = 256
        desired_w = grid_size * tile_size
        desired_h = grid_size * tile_size

        # Global pixel coordinates of the requested center
        center_px, center_py = _latlon_to_global_pixel(center_lat, center_lon, zoom_level, tile_size=tile_size)

        # Compute top-left pixel (global) so the center is centered in the final image
        top_left_px = int(math.floor(center_px - (desired_w / 2)))
        top_left_py = int(math.floor(center_py - (desired_h / 2)))

        # Determine the tile indices that fully cover the desired crop region
        tile_x_start = int(math.floor(top_left_px / tile_size))
        tile_y_start = int(math.floor(top_left_py / tile_size))
        tile_x_end = int(math.floor((top_left_px + desired_w - 1) / tile_size))
        tile_y_end = int(math.floor((top_left_py + desired_h - 1) / tile_size))

        tiles_x = tile_x_end - tile_x_start + 1
        tiles_y = tile_y_end - tile_y_start + 1

        # Create a canvas large enough for all requested tiles
        canvas_w = tiles_x * tile_size
        canvas_h = tiles_y * tile_size
        canvas = Image.new('RGB', (canvas_w, canvas_h))

        successful_downloads = 0
        for ty in range(tile_y_start, tile_y_end + 1):
            for tx in range(tile_x_start, tile_x_end + 1):
                # Use Esri World Imagery (satellite tiles) with tile args: zoom / y / x
                url = f"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{zoom_level}/{ty}/{tx}"
                try:
                    headers = {
                        'User-Agent': 'Field Boundary Detection API/1.0',
                        'Accept': 'image/*'
                    }
                    response = requests.get(url, timeout=15, headers=headers)
                    response.raise_for_status()
                    if response.content and len(response.content) > 1000:
                        tile_image = Image.open(io.BytesIO(response.content)).convert('RGB')
                        # paste into canvas at position relative to tile_x_start/tile_y_start
                        paste_x = (tx - tile_x_start) * tile_size
                        paste_y = (ty - tile_y_start) * tile_size
                        canvas.paste(tile_image, (paste_x, paste_y))
                        successful_downloads += 1
                    else:
                        logger.warning(f"Empty tile response for {tx},{ty}")
                        blank_tile = Image.new('RGB', (tile_size, tile_size), color=(128, 128, 128))
                        paste_x = (tx - tile_x_start) * tile_size
                        paste_y = (ty - tile_y_start) * tile_size
                        canvas.paste(blank_tile, (paste_x, paste_y))
                except requests.RequestException as e:
                    logger.warning(f"Failed to download tile {tx},{ty}: {e}")
                    blank_tile = Image.new('RGB', (tile_size, tile_size), color=(128, 128, 128))
                    paste_x = (tx - tile_x_start) * tile_size
                    paste_y = (ty - tile_y_start) * tile_size
                    canvas.paste(blank_tile, (paste_x, paste_y))

        logger.info(f"Downloaded {successful_downloads}/{tiles_x * tiles_y} tiles for crop")

        # Crop the canvas to the desired top-left pixel within the canvas
        crop_x = top_left_px - (tile_x_start * tile_size)
        crop_y = top_left_py - (tile_y_start * tile_size)
        # Ensure crop coordinates are within canvas bounds
        crop_x = max(0, min(crop_x, canvas_w - desired_w))
        crop_y = max(0, min(crop_y, canvas_h - desired_h))

        cropped = canvas.crop((crop_x, crop_y, crop_x + desired_w, crop_y + desired_h))
        image_array = np.array(cropped)

        # Compute the pixel coordinates of the requested center within the cropped image
        # center_px/py are global pixel coords; subtract top-left global pixel to get local coords
        center_px_local = center_px - float(top_left_px)
        center_py_local = center_py - float(top_left_py)
        # Clamp to image bounds
        center_px_local = float(max(0, min(center_px_local, desired_w - 1)))
        center_py_local = float(max(0, min(center_py_local, desired_h - 1)))

        # Compute bounding box in geographic coordinates for the cropped image
        # Top-left pixel global coords
        tl_global_x = top_left_px
        tl_global_y = top_left_py
        # Use inclusive pixel coordinates for bottom-right (last pixel inside the crop)
        br_global_x = top_left_px + desired_w - 1
        br_global_y = top_left_py + desired_h - 1

        tl_lat, tl_lon = _global_pixel_to_latlon(tl_global_x, tl_global_y, zoom_level, tile_size=tile_size)
        br_lat, br_lon = _global_pixel_to_latlon(br_global_x, br_global_y, zoom_level, tile_size=tile_size)

        bbox = {
            'xmin': float(tl_lon),
            'xmax': float(br_lon),
            'ymax': float(tl_lat),
            'ymin': float(br_lat)
        }

        return {
            'bbox': bbox,
            'image_array': image_array,
            'width': desired_w,
            'height': desired_h,
            'successful_downloads': successful_downloads,
            'total_tiles': tiles_x * tiles_y,
            'center_pixel': {'x': center_px_local, 'y': center_py_local}
        }

    except Exception as e:
        logger.error(f"Failed to download satellite image: {e}")
        raise

def upload_to_gcs(local_file_path, bucket_name, destination_blob_name, content_type=None):
    """
    Upload a file to Google Cloud Storage.
    
    Args:
        local_file_path: Path to the local file
        bucket_name: GCS bucket name
        destination_blob_name: Path inside the bucket
        content_type: MIME type of the file

    Returns:
        The GCS URI of the uploaded file
    """
    try:
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(destination_blob_name)
        
        if content_type:
            blob.content_type = content_type
        
        blob.upload_from_filename(local_file_path)
        
        gcs_uri = f"gs://{bucket_name}/{destination_blob_name}"
        logger.info(f"Uploaded {local_file_path} to {gcs_uri}")
        
        return gcs_uri
        
    except Exception as e:
        logger.error(f"Failed to upload to GCS: {str(e)}")
        raise

def upload_array_to_gcs(image_array, bucket_name, destination_blob_name, image_format='PNG'):
    """
    Upload a numpy array as an image to Google Cloud Storage.
    
    Args:
        image_array: Numpy array representing the image
        bucket_name: GCS bucket name
        destination_blob_name: Path inside the bucket
        image_format: Image format (PNG, JPEG, etc.)

    Returns:
        The GCS URI of the uploaded file
    """
    try:
        # Convert numpy array to image
        if len(image_array.shape) == 3 and image_array.shape[2] == 3:
            # RGB image
            image = Image.fromarray(image_array.astype(np.uint8))
        else:
            # Grayscale or other format
            image = Image.fromarray(image_array.astype(np.uint8), mode='L')
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix=f'.{image_format.lower()}', delete=False) as temp_file:
            temp_path = temp_file.name
            image.save(temp_path, format=image_format)
        
        # Upload to GCS
        content_type = f'image/{image_format.lower()}'
        gcs_uri = upload_to_gcs(temp_path, bucket_name, destination_blob_name, content_type)
        
        # Clean up temporary file
        os.unlink(temp_path)
        
        return gcs_uri
        
    except Exception as e:
        logger.error(f"Failed to upload array to GCS: {str(e)}")
        raise

def download_from_gcs(bucket_name, blob_name, local_file_path):
    """
    Download a file from Google Cloud Storage.
    
    Args:
        bucket_name: GCS bucket name
        blob_name: Path inside the bucket
        local_file_path: Local path to save the file

    Returns:
        Path to the downloaded file
    """
    try:
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(blob_name)
        
        blob.download_to_filename(local_file_path)
        logger.info(f"Downloaded gs://{bucket_name}/{blob_name} to {local_file_path}")
        
        return local_file_path
        
    except Exception as e:
        logger.error(f"Failed to download from GCS: {str(e)}")
        raise

def download_image_array_from_gcs(bucket_name, blob_name):
    """
    Download an image from Google Cloud Storage and return as numpy array.
    
    Args:
        bucket_name: GCS bucket name
        blob_name: Path inside the bucket

    Returns:
        Numpy array representing the image
    """
    try:
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(blob_name)
        
        # Download to temporary file
        with tempfile.NamedTemporaryFile() as temp_file:
            blob.download_to_filename(temp_file.name)
            
            # Load image
            image = cv2.imread(temp_file.name)
            if image is None:
                raise ValueError(f"Could not load image from {blob_name}")
            
            # Convert BGR to RGB
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
        logger.info(f"Downloaded and loaded image array from gs://{bucket_name}/{blob_name}")
        return image_rgb
        
    except Exception as e:
        logger.error(f"Failed to download image array from GCS: {str(e)}")
        raise

def create_geojson_from_contours(contours_list, metadata=None):
    """
    Create a GeoJSON FeatureCollection from a list of contour coordinate lists.
    
    Args:
        contours_list: List of contour coordinate lists
        metadata: Optional metadata to include

    Returns:
        GeoJSON FeatureCollection
    """
    features = []
    
    for i, contour_points in enumerate(contours_list):
        if not contour_points:
            continue
            
        # Ensure polygon is closed
        if contour_points[0] != contour_points[-1]:
            contour_points.append(contour_points[0])
        
        feature = {
            "type": "Feature",
            "properties": {
                "contour_id": i,
                "point_count": len(contour_points),
                **(metadata or {})
            },
            "geometry": {
                "type": "Polygon",
                "coordinates": [contour_points]
            }
        }
        features.append(feature)
    
    return {
        "type": "FeatureCollection",
        "features": features
    }

def validate_coordinates(lat, lon):
    """
    Validate latitude and longitude coordinates.
    
    Args:
        lat: Latitude value
        lon: Longitude value
        
    Returns:
        Tuple (is_valid, error_message)
    """
    try:
        lat = float(lat)
        lon = float(lon)
        
        if not (-90 <= lat <= 90):
            return False, f"Latitude {lat} is out of range [-90, 90]"
        
        if not (-180 <= lon <= 180):
            return False, f"Longitude {lon} is out of range [-180, 180]"
        
        return True, None
        
    except (ValueError, TypeError):
        return False, "Invalid coordinate format"